import 'package:flutter/material.dart';
import 'package.flutter_test/flutter_test.dart';
import 'package:dreamflow/navigation/app_navigation.dart';
import 'package:dreamflow/screens/chat_screen.dart';
import 'package:dreamflow/screens/settings_screen.dart';
import 'package:dreamflow/screens/statistics_screen.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';
import '../mocks/mock_storage_service.dart';

void main() {
  group('AppNavigation Widget Tests', () {
    Future<void> pumpAppNavigation(WidgetTester tester) async {
      final mockTransactionProvider = TestHelpers.createMockTransactionProvider();
      final mockStorageService = MockStorageService();
      final mockThemeProvider = TestHelpers.createMockThemeProvider(mockStorageService);
      
      // This is a simplified setup. A real test might need more complex providers.
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider.value(value: mockTransactionProvider),
            ChangeNotifierProvider.value(value: mockThemeProvider),
            Provider<StorageService>.value(value: mockStorageService),
            // Add mock for TransactionParsingService if needed by ChatScreen during init
          ],
          child: const MaterialApp(
            home: AppNavigation(),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('starts on ChatScreen', (WidgetTester tester) async {
      await pumpAppNavigation(tester);

      // Verify ChatScreen is visible
      expect(find.byType(ChatScreen), findsOneWidget);

      // Verify other screens are not visible
      expect(find.byType(StatisticsScreen), findsNothing);
      expect(find.byType(SettingsScreen), findsNothing);
    });

    testWidgets('can navigate to StatisticsScreen', (WidgetTester tester) async {
      await pumpAppNavigation(tester);

      // Tap the Statistics icon in the BottomNavigationBar
      await tester.tap(find.byIcon(Icons.bar_chart));
      await tester.pumpAndSettle();

      // Verify StatisticsScreen is now visible
      expect(find.byType(StatisticsScreen), findsOneWidget);
      expect(find.byType(ChatScreen), findsNothing);
    });

    testWidgets('can navigate to SettingsScreen', (WidgetTester tester) async {
      await pumpAppNavigation(tester);

      // Tap the Settings icon
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Verify SettingsScreen is now visible
      expect(find.byType(SettingsScreen), findsOneWidget);
      expect(find.byType(ChatScreen), findsNothing);
    });

    testWidgets('navigation preserves screen state due to IndexedStack', (WidgetTester tester) async {
      await pumpAppNavigation(tester);

      // Initial screen is Chat
      expect(find.byType(ChatScreen), findsOneWidget);

      // Navigate to Statistics
      await tester.tap(find.byIcon(Icons.bar_chart));
      await tester.pumpAndSettle();
      expect(find.byType(StatisticsScreen), findsOneWidget);

      // Navigate to Settings
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();
      expect(find.byType(SettingsScreen), findsOneWidget);

      // Navigate back to Chat
      await tester.tap(find.byIcon(Icons.chat_bubble));
      await tester.pumpAndSettle();
      expect(find.byType(ChatScreen), findsOneWidget);

      // Because of IndexedStack, all three screens are actually in the widget tree,
      // just not visible. We can find them offstage.
      expect(find.byType(ChatScreen, skipOffstage: false), findsOneWidget);
      expect(find.byType(StatisticsScreen, skipOffstage: false), findsOneWidget);
      expect(find.byType(SettingsScreen, skipOffstage: false), findsOneWidget);
    });
  });
}
