import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/widgets/transaction_edit_dialog.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';

void main() {
  group('TransactionEditDialog Widget Tests', () {
    late Transaction mockTransaction;
    late TransactionProvider mockTransactionProvider;

    setUp(() {
      mockTransaction = TestHelpers.createTestTransaction(
        id: 'txn1',
        description: 'Initial Description',
        amount: -50.0,
        type: TransactionType.expense,
        categoryId: 'food',
        currencyCode: 'USD',
      );
      mockTransactionProvider = TestHelpers.createMockTransactionProvider();
    });

    Future<void> pumpDialog(WidgetTester tester) async {
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: mockTransactionProvider,
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => TextButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) => TransactionEditDialog(
                        transaction: mockTransaction,
                        onSave: (updatedTx) {
                          mockTransaction = updatedTx;
                        },
                      ),
                    );
                  },
                  child: const Text('Show Dialog'),
                ),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();
    }

    testWidgets('renders correctly and pre-fills with transaction data', (WidgetTester tester) async {
      await pumpDialog(tester);

      expect(find.text('Edit Transaction'), findsOneWidget);

      // Verify amount is pre-filled
      expect(find.widgetWithText(TextFormField, '50.00'), findsOneWidget);

      // Verify description is pre-filled
      expect(find.widgetWithText(TextFormField, 'Initial Description'), findsOneWidget);

      // Verify category is selected
      expect(find.text('Food & Drink'), findsOneWidget);

      // Verify transaction type is selected
      expect(find.text('Expense'), findsOneWidget);

      expect(find.widgetWithText(TextButton, 'Cancel'), findsOneWidget);
      expect(find.widgetWithText(ElevatedButton, 'Save'), findsOneWidget);
    });

    testWidgets('can edit amount and description', (WidgetTester tester) async {
      await pumpDialog(tester);

      // Edit amount
      await tester.enterText(find.byKey(const Key('amount_field')), '75.25');
      await tester.pump();

      // Edit description
      await tester.enterText(find.byKey(const Key('description_field')), 'Updated Description');
      await tester.pump();

      expect(find.text('75.25'), findsOneWidget);
      expect(find.text('Updated Description'), findsOneWidget);
    });

    testWidgets('tapping Save calls onSave with updated data', (WidgetTester tester) async {
      await pumpDialog(tester);

      await tester.enterText(find.byKey(const Key('amount_field')), '100');
      await tester.enterText(find.byKey(const Key('description_field')), 'New Purchase');

      await tester.tap(find.widgetWithText(ElevatedButton, 'Save'));
      await tester.pumpAndSettle();

      // Verify the onSave callback was called with the updated transaction
      expect(mockTransaction.amount, 100.0);
      expect(mockTransaction.description, 'New Purchase');
      expect(find.byType(TransactionEditDialog), findsNothing); // Dialog is closed
    });

    testWidgets('tapping Cancel closes the dialog without changes', (WidgetTester tester) async {
      await pumpDialog(tester);

      await tester.enterText(find.byKey(const Key('amount_field')), '100');

      await tester.tap(find.widgetWithText(TextButton, 'Cancel'));
      await tester.pumpAndSettle();

      // Verify the transaction was not changed
      expect(mockTransaction.amount, -50.0);
      expect(find.byType(TransactionEditDialog), findsNothing); // Dialog is closed
    });
  });
}
