import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/widgets/transaction_message.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';

void main() {
  group('TransactionMessage Widget Tests', () {
    late Transaction mockTransaction;
    late TransactionProvider mockTransactionProvider;
    late Category mockCategory;

    setUp(() {
      mockTransaction = TestHelpers.createTestTransaction(
        id: 'txn1',
        description: 'Coffee with friends',
        amount: -15.50,
        type: TransactionType.expense,
        categoryId: 'food',
        currencyCode: 'USD',
      );
      mockCategory = TestHelpers.createTestCategory(
        id: 'food',
        name: 'Food',
        type: TransactionType.expense,
      );
      mockTransactionProvider = TestHelpers.createMockTransactionProvider();
    });

    Future<void> pumpWidget(WidgetTester tester) async {
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: mockTransactionProvider,
          child: MaterialApp(
            home: Scaffold(
              body: TransactionMessage(
                transaction: mockTransaction,
                category: mockCategory,
                onEdit: (transaction) {},
                onDelete: (id) {},
              ),
            ),
          ),
        ),
      );
    }

    testWidgets('renders correctly with expense transaction', (WidgetTester tester) async {
      await pumpWidget(tester);

      // Verify description is displayed
      expect(find.text('Coffee with friends'), findsOneWidget);

      // Verify formatted amount is displayed
      expect(find.text('-\$15.50'), findsOneWidget);

      // Verify category icon and name are displayed
      expect(find.byIcon(Icons.fastfood), findsOneWidget);
      expect(find.text('Food & Drink'), findsOneWidget);

      // Verify Edit and Delete buttons are present
      expect(find.widgetWithText(TextButton, 'Edit'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Delete'), findsOneWidget);
    });

    testWidgets('renders correctly with income transaction', (WidgetTester tester) async {
      mockTransaction = TestHelpers.createTestTransaction(
        id: 'txn2',
        description: 'Monthly Salary',
        amount: 3000.00,
        type: TransactionType.income,
        categoryId: 'salary',
        currencyCode: 'EUR',
      );

      await pumpWidget(tester);

      // Verify description
      expect(find.text('Monthly Salary'), findsOneWidget);

      // Verify formatted amount for income
      expect(find.text('+€3,000.00'), findsOneWidget);

      // Verify category icon and name
      expect(find.byIcon(Icons.work), findsOneWidget);
      expect(find.text('Salary'), findsOneWidget);
    });

    testWidgets('tapping Delete button shows confirmation dialog', (WidgetTester tester) async {
      await pumpWidget(tester);

      await tester.tap(find.widgetWithText(TextButton, 'Delete'));
      await tester.pumpAndSettle(); // Wait for dialog animation

      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Confirm Deletion'), findsOneWidget);
      expect(find.text('Are you sure you want to delete this transaction?'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Cancel'), findsOneWidget);
      expect(find.widgetWithText(TextButton, 'Delete'), findsOneWidget);
    });

    testWidgets('tapping Edit button shows edit dialog', (WidgetTester tester) async {
      await pumpWidget(tester);

      await tester.tap(find.widgetWithText(TextButton, 'Edit'));
      await tester.pumpAndSettle();

      // A proper test would check for the TransactionEditDialog,
      // but for this test, we'll just confirm a dialog appears.
      // This assumes the edit dialog is also a Dialog.
      expect(find.byType(Dialog), findsOneWidget);
      expect(find.text('Edit Transaction'), findsOneWidget);
    });
  });
}
