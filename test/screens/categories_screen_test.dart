import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:money_lover_chat/models/transaction_model.dart';
import 'package:money_lover_chat/screens/categories_screen.dart';
import 'package:provider/provider.dart';

import '../helpers/test_helpers.dart';

void main() {
  group('CategoriesScreen Widget Tests', () {
    late TransactionProvider mockTransactionProvider;

    setUp(() {
      mockTransactionProvider = TestHelpers.createMockTransactionProvider();
    });

    Future<void> pumpScreen(WidgetTester tester) async {
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: mockTransactionProvider,
          child: const MaterialApp(
            home: CategoriesScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('renders correctly with list of categories', (WidgetTester tester) async {
      await pumpScreen(tester);

      expect(find.text('Categories'), findsOneWidget);

      // Verify that the tabs for transaction types are present
      expect(find.widgetWithText(Tab, 'Expense'), findsOneWidget);
      expect(find.widgetWithText(Tab, 'Income'), findsOneWidget);
      expect(find.widgetWithText(Tab, 'Loan'), findsOneWidget);

      // Verify that expense categories are shown by default
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Shopping'), findsOneWidget);
      expect(find.text('Transport'), findsOneWidget);
      
      // Verify an income category is not initially visible
      expect(find.text('Salary'), findsNothing);
    });

    testWidgets('can switch to Income tab and view income categories', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Initially on Expense tab
      expect(find.text('Food & Drink'), findsOneWidget);
      expect(find.text('Salary'), findsNothing);

      // Tap the 'Income' tab
      await tester.tap(find.widgetWithText(Tab, 'Income'));
      await tester.pumpAndSettle();

      // Verify income categories are now visible
      expect(find.text('Salary'), findsOneWidget);
      
      // Verify expense categories are no longer visible
      expect(find.text('Food & Drink'), findsNothing);
    });

    testWidgets('shows empty state if no categories exist for a type', (WidgetTester tester) async {
      // Create a provider with no loan categories
      mockTransactionProvider = TestHelpers.createMockTransactionProvider(
        categories: TestHelpers.createDefaultCategories().where((c) => c.type != TransactionType.loan).toList()
      );
      
      await pumpScreen(tester);

      // Switch to the 'Loan' tab
      await tester.tap(find.widgetWithText(Tab, 'Loan'));
      await tester.pumpAndSettle();

      expect(find.text('No categories available for this type.'), findsOneWidget);
    });
  });
}
