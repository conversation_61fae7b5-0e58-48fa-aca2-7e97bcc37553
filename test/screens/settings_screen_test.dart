import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:money_lover_chat/screens/settings_screen.dart';
import 'package:money_lover_chat/services/storage_service.dart';
import 'package:money_lover_chat/theme.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../mocks/mock_storage_service.dart';

void main() {
  group('SettingsScreen Widget Tests', () {
    late MockStorageService mockStorageService;
    late ThemeProvider themeProvider;

    setUp(() async {
      // Set up mock SharedPreferences
      SharedPreferences.setMockInitialValues({
        'theme': 'light',
        'default_currency': 'USD',
      });
      final prefs = await SharedPreferences.getInstance();
      mockStorageService = MockStorageService(prefs);
      
      // Initialize ThemeProvider with the mock storage
      themeProvider = ThemeProvider(mockStorageService);
      await themeProvider.loadTheme();
    });

    Future<void> pumpScreen(WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider.value(value: themeProvider),
            Provider<StorageService>.value(value: mockStorageService),
          ],
          child: const MaterialApp(
            home: SettingsScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('renders correctly and shows initial settings', (WidgetTester tester) async {
      await pumpScreen(tester);

      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Currency'), findsOneWidget);

      // Verify initial theme is light
      expect(find.text('Light'), findsOneWidget);

      // Verify initial currency is USD
      expect(find.text('USD - United States Dollar'), findsOneWidget);
    });

    testWidgets('can change theme to Dark', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Tap the theme dropdown
      await tester.tap(find.text('Light'));
      await tester.pumpAndSettle();

      // Select 'Dark'
      await tester.tap(find.text('Dark').last);
      await tester.pumpAndSettle();

      // Verify the theme provider's state has changed
      expect(themeProvider.themeMode, ThemeMode.dark);
      // Verify the UI has updated
      expect(find.text('Dark'), findsOneWidget);
    });

    testWidgets('can change currency to EUR', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Tap the currency dropdown
      await tester.tap(find.text('USD - United States Dollar'));
      await tester.pumpAndSettle();

      // Select 'EUR'
      await tester.tap(find.text('EUR - Euro').last);
      await tester.pumpAndSettle();

      // Verify the change was saved to storage
      expect(await mockStorageService.getDefaultCurrency(), 'EUR');
      // Verify the UI has updated
      expect(find.text('EUR - Euro'), findsOneWidget);
    });
  });
}
